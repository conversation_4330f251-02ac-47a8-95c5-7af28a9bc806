#!/usr/bin/env python3
"""
Test script to verify the retroactive period logic implementation
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'server'))

from main import calculate_quotation

def test_retroactive_logic():
    """Test the retroactive period logic with different scenarios"""
    
    # Base test data
    base_proposal_summary = {
        "combined": {
            "proposal_summary": {
                "headers": [
                    "Name of Cedant",
                    "Name of Broker", 
                    "Period of Cover ",
                    "Number of Business Partners",
                    "Number of Qualified staff",
                    "Number of Unqualified Staff Employed",
                    "Estimated Annual Income",
                    "Limit of Indemnity (Cover Limit)",
                    "Occupation of the Insured",
                    "Deductible/Excess Applicable",
                    "Retroactive period"
                ],
                "values": [
                    "Test Company Ltd",
                    "Test Broker Ltd",
                    "1 Year",
                    2,
                    3,
                    1,
                    5000000,
                    ********,
                    "accountants",
                    100000,
                    0  # Will be modified for each test
                ]
            },
            "extensions": {
                "headers": [
                    "Defamation Coverage",
                    "Loss of Documents", 
                    "Errors and ommissions",
                    "Incoming/Outgoing partners",
                    "Dishonesty of employees",
                    "Breach of Authority"
                ],
                "values": [
                    "No",
                    "No",
                    "No", 
                    "No",
                    "No",
                    "No"
                ]
            },
            "insurance_history": {
                "headers": ["Previous Claims", "Previous Declined", "Previous Terminated", "Previous Refused", "Claims Settled", "Claim Awareness"],
                "values": ["No", "No", "No", "No", "No", "No"]
            }
        }
    }
    
    # Test scenarios
    test_scenarios = [
        {"retroactive_period": 0, "expected_adjustment_rate": 0, "description": "No retroactive period"},
        {"retroactive_period": 12, "expected_adjustment_rate": 0.40, "description": "12 months retroactive period (40% additional)"},
        {"retroactive_period": 24, "expected_adjustment_rate": 0.65, "description": "24 months retroactive period (65% additional)"},
        {"retroactive_period": 6, "expected_adjustment_rate": 0, "description": "6 months retroactive period (no adjustment)"},
        {"retroactive_period": 36, "expected_adjustment_rate": 0, "description": "36 months retroactive period (no adjustment)"}
    ]
    
    print("Testing Retroactive Period Logic")
    print("=" * 50)
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\nTest {i}: {scenario['description']}")
        print("-" * 40)
        
        # Set the retroactive period for this test
        test_data = base_proposal_summary.copy()
        test_data["combined"]["proposal_summary"]["values"][10] = scenario["retroactive_period"]
        
        # Calculate quotation
        result = calculate_quotation(test_data)
        
        if "error" in result:
            print(f"ERROR: {result['error']}")
            continue
            
        # Extract relevant values
        retroactive_period = result["quotation_calculation"]["retroactive_period"]
        retroactive_adjustment = result["quotation_calculation"]["retroactive_adjustment"]
        part_k = result["variables"]["part_k"]
        
        print(f"Retroactive Period: {retroactive_period} months")
        print(f"Retroactive Adjustment: {retroactive_adjustment}")
        print(f"Final Part K: {part_k}")
        
        # Verify the logic
        if scenario["expected_adjustment_rate"] == 0:
            expected_adjustment = 0
        else:
            # Calculate what the original part_k would have been
            original_part_k = part_k / (1 + scenario["expected_adjustment_rate"])
            expected_adjustment = original_part_k * scenario["expected_adjustment_rate"]
        
        print(f"Expected Adjustment Rate: {scenario['expected_adjustment_rate'] * 100}%")
        
        # Check if the adjustment is approximately correct
        if abs(retroactive_adjustment - expected_adjustment) < 0.01:
            print("✅ PASS: Retroactive adjustment is correct")
        else:
            print(f"❌ FAIL: Expected adjustment ~{expected_adjustment:.2f}, got {retroactive_adjustment}")

if __name__ == "__main__":
    test_retroactive_logic()
