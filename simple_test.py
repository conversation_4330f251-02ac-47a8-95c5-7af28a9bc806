#!/usr/bin/env python3
"""
Simple test to verify the retroactive period logic
"""

# Test data with retroactive period
test_proposal_summary = {
    "combined": {
        "proposal_summary": {
            "headers": [
                "Name of Cedant",
                "Name of Broker", 
                "Period of Cover ",
                "Number of Business Partners",
                "Number of Qualified staff",
                "Number of Unqualified Staff Employed",
                "Estimated Annual Income",
                "Limit of Indemnity (Cover Limit)",
                "Occupation of the Insured",
                "Deductible/Excess Applicable",
                "Retroactive period"
            ],
            "values": [
                "Test Company Ltd",
                "Test Broker Ltd",
                "1 Year",
                2,
                3,
                1,
                5000000,
                ********,
                "accountants",
                100000,
                12  # 12 months retroactive period
            ]
        },
        "extensions": {
            "headers": [
                "Defamation Coverage",
                "Loss of Documents", 
                "Errors and ommissions",
                "Incoming/Outgoing partners",
                "Dishonesty of employees",
                "Breach of Authority"
            ],
            "values": [
                "No",
                "No",
                "No", 
                "No",
                "No",
                "No"
            ]
        },
        "insurance_history": {
            "headers": ["Previous Claims"],
            "values": ["No"]
        }
    }
}

print("Test data created successfully!")
print(f"Retroactive period in test data: {test_proposal_summary['combined']['proposal_summary']['values'][10]}")
